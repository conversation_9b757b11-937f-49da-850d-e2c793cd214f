---
metadata:
  email:
    subject: >-
      {{{@root.request.request.property.name}}} | Ειδοποίηση |
      Προσφορά:{{@root.request.id}}
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>  {{@root.request.contact.nickname}},</p><p>   Ελπίζουμε να είσαστε καλά.</p><p> Σας στέλνουμε μια φιλική υπενθύμιση ότι λήγει η προσφορά - Προσφορά:{{@root.request.id}} - που σας έχουμε αποστείλει.</p><p> Αν ενδιαφέρεστε να προχωρήσετε με την κράτηση σας θα πρέπει να την αποδεχθείτε εντός της ημέρας.</p><p>  {{> proposalUrl . }}</p><p>   Αν για οποιονδήποτε λόγο επιθυμείτε παράταση της προθεσμίας, ενημερώστε μας εγκαίρως ώστε να εξετάσουμε αυτή τη δυνατότητα.</p><p>Εν αναμονή της απάντησης σας.</p><p>Με εκτίμηση,</p><p>{{operator.nickName}}</p>{{/emailLayout}}