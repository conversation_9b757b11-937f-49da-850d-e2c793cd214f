---
metadata:
  email:
    subject: >-
      {{{@root.request.request.property.name}}} | Υπενθύμιση |
      Προσφορά:{{@root.request.id}}
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>  {{@root.request.contact.nickname}},</p><p>  Ελπίζουμε να είσαστε καλά.</p><p>Σε συνέχεια της επικοινωνίας μας, θέλαμε να σας ρωτήσουμε αν βρήκατε τον χρόνο να εξετάσετε την προσφορά που σας είχαμε στείλει.</p><p>  {{> proposalUrl . }}</p><p>- Προσφορά:{{@root.request.id}} -</p><p>  {{> onHoldInfo . }}</p><p>  Σε περίπτωση που χρειάζεστε κάποια διευκρίνηση, παρακαλούμε μην διστάσετε να επικοινωνήσετε μαζί μας.</p><p>Εν αναμονή της απάντησης σας.</p><p> Με εκτίμηση,</p><p>{{operator.nickName}}</p>{{/emailLayout}}