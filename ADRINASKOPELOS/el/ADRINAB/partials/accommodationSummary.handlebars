{{#each @root.request.offers as |offer offerKey|}}
    {{#if offer.accepted}}
        <table>
            <tr>
                <td>Δωμάτιο:</td>
                <td>{{offer.rooms}} x {{offer.accommodation.[0].name}} - {{offer.rate.name}}</td>
            </tr>
            <tr>
                <td>Checkin - Checkout:</td>
                <td>{{ formatPeriod offer ../request.language
                                    metadata.formats.period }}
            </td>
            </tr>
            <tr>
                <td>Νύχτες:</td>
                <td>{{ pluralize offer.nights 'Night' 'Nights' }}</td>
            </tr>
            <tr>
                <td>Άτομα:</td>
                <td>
                    {{
                    separateWithComma
                            (pluralize offer.adults 'Ενήλικας' 'Ενήλικες')
                            (pluralize offer.children 'Παιδί' 'Παιδιά')
                            (pluralize offer.infants 'Βρέφος' 'Βρέφοι')
                    }}
                </td>
            </tr>
            <tr>
                <td>Τιμή:</td>
                <td>{{ formatMoney offer.roomRate offer.currency }}</td>
            </tr>
            {{#compare offer.excludedCharges ">" 0 }}
            <tr>
                <td>Τέλος Ανθεκτικότητας στην Κλιματική Κρίση:</td>
                <td>{{ formatMoney (multiply offer.excludedCharges offer.nights ) offer.currency }}</td>
            </tr>
            {{/compare}}
            <tr>
                <td>Σύνολο Διαμονής:</td>
                <td>{{ formatMoney (calcTotalPrice offer) offer.currency }}</td>
            </tr>
            {{#if offer.services.length}}
                <tr>
                    <td>Παροχές:</td>
                    <td>{{ join ',' offer.services 'name' }}</td>
                </tr>
            {{/if}}
        </table>
    {{/if}}
{{/each}}
