<div class='ui basic segment' style='background-color: {{@root.metadata.header.backgroundColor}};'>
  <div class='ui container stackable grid'>
    <div class='row'>
      <div class='twelve wide column disabled'>
        <div class='ui items'>
          <div class='item'>
            <div class='content'>
              {{#isOnHold request}}
              <div class='header' style='color: {{metadata.header.color}};'>
                Τα παραπάνω δωμάτια έχουν δεσμευτεί για εσάς μέχρι τις {{formatDate request.request.expireAt metadata.formats.date}}
              </div>
              {{else}}
              <div class='header' style='color: {{metadata.header.color}};'>
                Τα παραπάνω δωμάτια δεν έχουν δεσμευτεί για αυτό είναι σημαντικό να μας απαντήσετε το συντομότερο δυνατόν.
              </div>
              {{/isOnHold}}
        <div class='meta' style='color: {{metadata.header.color}};'>
                Αν δεν μείνατε ικανοποιημένοι από την προσφορά μας παρακαλώ ενημερώστε μας.
              </div>
              <div class='extra' style='color: {{metadata.header.color}};'>
                <button class='ui negative reject disabled button' id='reject-main-button'>
                  ΔΕΝ ΑΠΟΔΕΧΟΜΑΙ ΤΗΝ ΠΡΟΣΦΟΡΑ
                </button>
              </div>  
            </div>
          </div>
        </div>
      </div>
      <div class='four wide column computer only right aligned'>
        <img src='{{ metadata.property.logourl}}' alt='{{property.name}}' />
      </div>
    </div>
    <div class='expanded row'>
      <div class='sixteen wide column mobile only center aligned'>
        <div class='ui message'>
          Παρακαλώ επισκεφτείτε αυτή την σελίδα με τον υπολογιστή σας ή το τάμπλετ σας για να δείτε περισσότερες πληροφορίες
        </div>
      </div>
    </div>
  </div>
</div>
