---
metadata:
  email:
    subject: >-
      {{{@root.request.request.property.name}}} | Ειδοποίηση |
      Προσφορά:{{@root.request.id}}
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>  {{@root.request.contact.nickname}},</p><p>   Ελπίζουμε να είσαστε καλά.</p><p> Σε συνέχεια της επικοινωνίας μας, και την αποδοχή της προσφοράς που σας είχαμε στείλει  - Προσφορά:{{@root.request.id}} , θα θέλαμε να σας ενημερώσουμε ότι δεν έχουμε λάβει ακόμα κάποια ειδοποίηση αναφορικά με την πληρωμή.</p><p> Όταν προχωρήσετε με την πληρωμή θα σας παρακαλούσαμε να επικοινωνήσετε μαζί μας προκειμένου να επιβεβαιώσουμε την κράτησή σας.</p><p> Εν αναμονή της απάντησης σας.</p><p> Με εκτίμηση,</p><p>{{operator.nickName}}</p>{{/emailLayout}}