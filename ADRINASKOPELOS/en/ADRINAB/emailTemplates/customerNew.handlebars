---
metadata:
  email:
    subject: '{{{@root.request.request.property.name}}} | Offer:{{@root.request.id}}'
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>Dear {{@root.request.contact.nickname}},</p><p>Thank you for your email and your interest in our hotels.</p><p>Following our communication, we have prepared an offer for your stay at our hotel with the availability that is currently in effect and uploaded it on our secure server for your perusal. Please note that due to the online booking system, availability might change anytime without notice.</p><p> In order to view the offer we have prepared for you, please click on the link below.</p><p>  {{> proposalUrl . }}</p><p>- Offer:{{@root.request.id}} -</p><p>  {{> onHoldInfo . }}</p><p>We remain at your disposal and do not hesitate to contact us directly if you have any questions.</p><p>Looking forward to your answer.</p><p>Efharisto poli (thank you very much),</p><p>{{operator.nickName}}</p>{{/emailLayout}}