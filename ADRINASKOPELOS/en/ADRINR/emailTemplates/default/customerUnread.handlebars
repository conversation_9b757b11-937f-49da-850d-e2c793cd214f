---
metadata:
  email:
    subject: >-
      {{{@root.request.request.property.name}}} | Follow Up |
      Offer:{{@root.request.id}}
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>  Dear {{@root.request.contact.nickname}},</p><p>  We hope that our email finds you really well!</p><p>  Further to our previous communication, we are writing in order to find out if you had the chance to check our proposal and whether it was to your liking.</p><p>  {{> proposalUrl . }}</p><p>- Offer:{{@root.request.id}} -</p><p>  Should you wish us to check other information about your relaxing holidays at {{@root.request.request.property.name}}, feel free to email us directly and we will be happy to be of assistance.</p><p>  Kindest regards,</p><p>{{operator.nickName}}</p>{{/emailLayout}}