---
metadata:
  email:
    subject: >-
      {{{@root.request.request.property.name}}} | Notice |
      Offer:{{@root.request.id}}
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>  Dear {{@root.request.contact.nickname}},</p><p>  How are you today?</p><p>  We are just sending you a quick note to remind you that tomorrow our Offer:{{@root.request.id}} expires.</p><p>  If you want to proceed with your booking, now it is a good time to reserve your room.</p><p>  {{> proposalUrl . }}</p><p>  In case that you are still interested in booking your holidays at {{@root.request.request.property.name}} and you need more time to consider your options, please do let us know so that we can check availability for you and see if we can extend the release date.</p><p>  Look forward to receiving your reply.</p><p>  Kindest regards,</p><p>{{operator.nickName}}</p>{{/emailLayout}}