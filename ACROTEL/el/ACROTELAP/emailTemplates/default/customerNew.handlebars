---
  metadata:
    email:
      subject: '{{{@root.request.request.property.name}}} | Προσφορά'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      bcc: '{{request.request.property.contact.bcc}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  {{@root.request.contact.nickname}},
</p>
<p>
  Σε συνέχεια της επικοινωνίας μας, ετοίμασα και σας αποστέλλω μια προσφορά για την διαμονή σας στο ξενοδοχείο μας.
</p>
<p>
  Προκειμένου να δείτε την προσφορ<PERSON> που σας έχω ετοιμάσει, πα<PERSON><PERSON><PERSON>αλώ πατήστε στον παρακάτω σύνδεσμο.
</p>
<p>
   {{> proposalUrl . }}
</p>
<p>
  {{> onHoldInfo . }}
</p>
<p>
  Παραμένω στην διάθεση σας για οποιαδήποτε διευκρίνιση.
</p>
<p>
  Εν αναμονή της απάντησης σας.
</p>
<p>
  Με εκτίμηση,
</p>

{{/emailLayout}}