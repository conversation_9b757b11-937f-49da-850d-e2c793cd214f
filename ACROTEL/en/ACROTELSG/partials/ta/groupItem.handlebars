<div class='row offer-block' data-option-id='{{groupId}}' id='offer-{{groupId}}-block'>
  <div class='row'>
    <div class='ui compact flatten segment'>
      <div class='ui stackable divided grid'>
        <div class='row'>
          <div class='ten wide tablet twelve wide column'>
            <h2 class='ui header text-uc'>
              {{group.0.title}}
            </h2>
            <h3 class='ui header emphasize text-uc'>
               {{group.0.description}}
            </h3>
            {{#each (uniqueOptionOffersAccommodation group) as |room roomIndex|}}
              <div class='ui grid'>
                <div class='sixteen wide mobile nine wide computer column'>
                  <a class='ui large image' href='#'>
                    <img class='ui visible image' src='{{ room.photos.[0].medium }}' alt='{{room.name}}'>
                  </a>
                </div>
                <div class='sixteen wide mobile seven wide computer column'>
                  <div class='ui header h4'>
                    <a class='text-black' href='#'>
                      {{room.name}}
                    </a>
                  </div>
                  <div class='ui'>
                    {{{summarySplit room.description "<!--summary-->" "first"}}}
                  </div>
                </div>
              </div>
            {{/each}}
            {{#if group.0.rate.board.description }}
              {{#if group.0.rate.board.photo}}
                <div class='ui grid'>
                <div class='seven wide column computer only'>
                  <a class='ui image' href='#'>
                    <img class='ui rounded image' src='{{group.0.rate.board.photo}}'>
                  </a>
                </div>
                <div class='sixteen wide mobile nine wide computer column'>
                  <div class='ui header'>
                    <a class='ui small header' href='#'>
                      {{group.0.rate.board.type}}
                    </a>
                  </div>
                  <div class='description'>
                    {{group.0.rate.board.description}}
                  </div>
                </div>
              </div>
              {{/if}}
            {{/if}}
            {{#each (uniqueOptionOffersServices group) as |service serviceIndex|}}
              <div class='ui grid'>
                <div class='seven wide column computer only'>
                  <a class='ui image' href='#'>
                    <img class='ui rounded image' src='{{service.photos.[0].medium}}' alt='{{service.name}}'>
                  </a>
                </div>
                <div class='sixteen wide mobile nine wide computer column'>
                  <div class='ui header'>
                    <a class='ui small header' href='#'>
                      {{service.name}}
                    </a>
                  </div>
                  <div class='description'>
                    {{{service.description}}}
                  </div>
                </div>
              </div>
            {{/each}}
            <div class='ui basic segment'>
                {{{group.0.rate.presentation}}}
            </div>
          </div>
          <div class='sixteen wide tablet mobile four wide computer column'>
            {{#each group as |offer offerKey|}}
              <div class='ui vertical segment center aligned'>
                <h3>{{ offer.accommodation.0.name}}</h3>
                <p>{{ formatPeriod offer @root.request.language metadata.formats.period }}</p>
                <p>{{ separateWithComma (pluralize offer.rooms 'Room' 'Rooms') (pluralize offer.adults 'Adult' 'Adults') (pluralize offer.children
                'Child' 'Children') (pluralize offer.infants 'Infant' 'Infants') (pluralize offer.nights 'Night' 'Nights') ' per room '
                }}</p>
            </div>
            {{/each}}
            {{#ifLess (calcOptionRoomRate group) (calcOptionOfficialRate group) }}
              <div class='ui vertical segment center aligned'>
                STANDARD RATE
                <div class='ui strong'>
                  <del>{{ formatMoney (calcOptionOfficialRate ../group) ../group.0.currency }}</del>
                </div>
              </div>
              <div class='ui vertical segment center aligned'>
                <div class='ui strong emphasize'>
                  {{ calcOptionDiscountRate ../group }} % COMMISSION
                </div>
              </div>
            {{/ifLess}}
            <div class='ui vertical segment center aligned'>
              Accommodation Total: {{ formatMoney (calcOptionRoomRate group) group.0.currency }}
            </div>
            {{#compare (calcOptionExcludedCharges group) ">" 0 }}
              <div class='ui vertical segment center aligned'>
                Climate Resilience Tax: {{ formatMoney (calcOptionExcludedCharges group) group.0.currency }}
              </div>
            {{/compare}}
            {{#compare (calcOptionServicesTotalRate group) ">" 0 }}
              <div class='ui vertical segment center aligned'>
                Service Total: {{ formatMoney (calcOptionServicesTotalRate group) group.0.currency }}
              </div>
            {{/compare}}
            <div class='ui vertical segment center aligned'>
              Total: 
              <div>
                <strong class='ui header huge'>{{ formatMoney (calcOptionTotalRate group) group.0.currency }}</strong>
              </div>
            </div>
            {{> acceptButton offer=group.[0] }}
            {{> policyLink offer=group.[0] }}
          </div>
        </div>
      </div>
    </div>
    <div class='ui hidden divider test'></div>
  </div>
</div>
