---
  metadata:
    email:
      subject: '{{{@root.request.request.property.name}}} | Greetings'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      bcc: '{{request.request.property.contact.bcc}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  Dear {{@root.request.contact.nickname}},
</p>
<p>
  We hope that our email finds you really well!
</p>
<p>
  We are contacting you further to the offer that we have sent concerning your vacation in Halkidiki. 
</p>
<p>
  {{> proposalUrl . }}
</p>
<p>
  We would be eager to receive your feedback and be informed if our offer suits you, or if we could propose you something else, closer to your needs. In case you need additional time to think of it, just let us know, if on the other side you need further clarifications or to update your request, feel free to contact us directly.
</p>
<p>
    We remain at your disposal.
</p>
<p>
  Kindest regards,
</p>

{{/emailLayout}}