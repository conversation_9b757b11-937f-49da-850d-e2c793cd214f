---
  metadata:
    email:
      subject: '{{{request.request.property.name}}} | Payment Confirmation'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      bcc: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  Dear {{request.contact.nickname}},
</p>
<p>
  I'm happy to confirm your reservation regarding your stay on {{formatDate request.request.checkin metadata.formats.humanDate request.language}}.
</p>
<p>
  Your booking and all the services and amenities we have discussed are now confirmed!
</p>
<p>
  Please find below the accommodation summary for your records.
</p>
<h4>ACCOMMODATION SUMMARY</h4>
<h5>Reference Number: {{ request.id }}</h5>
{{#each request.offers as |offer offerKey|}}
    {{#if offer.accepted}}
        <table>
            <tr>
                <td>Accommodation:</td>
                <td>{{offer.rooms}} x {{offer.accommodation.[0].name}}</td>
            </tr>
            <tr>
                <td>Checkin - Checkout:</td>
                <td>{{ formatPeriod offer ../request.language
                                    metadata.formats.period }}
            </td>
            </tr>
            <tr>
                <td>Nights:</td>
                <td>{{ pluralize offer.nights 'Night' 'Nights' }}</td>
            </tr>
            <tr>
                <td>Total pax:</td>
                <td>
                    {{
                    separateWithComma
                            (pluralize offer.adults 'Adult' 'Adults')
                            (pluralize offer.children 'Child' 'Children')
                            (pluralize offer.infants 'Infant' 'Infants')
                    }}
                </td>
            </tr>
            <tr>
                <td>Rate:</td>
                <td>{{ formatMoney offer.roomRate offer.currency }}</td>
            </tr>
            <tr>
                <td>Accommodation Total:</td>
                <td>{{ formatMoney (calcTotalPrice offer) offer.currency }}</td>
            </tr>
            {{#if offer.services.length}}
                <tr>
                    <td>Services:</td>
                    <td>{{ join ',' offer.services 'name' }}</td>
                </tr>
            {{/if}}
        </table>
        <p>{{{offer.property.disclaimer.externally_collected_taxes}}}</p>
    {{/if}}
{{/each}}
<p>
  Thank you once again for choosing {{request.request.property.name}} for your carefree holidays. It was my pleasure communicating with you and of course, I remain at your disposal for any additional information or assistance you may require.
</p>
<p>
  Kindest regards,
</p>

{{/emailLayout}}