<div class='ui basic segment' style='background-color: {{@root.metadata.header.backgroundColor}};'>
  <div class='ui container stackable grid'>
    <div class='row'>
      <div class='twelve wide column disabled'>
        <div class='ui items'>
          <div class='item'>
            <div class='content'>
              {{#isOnHold request}}
              <div class='header' style='color: {{metadata.header.color}};'>
                These rooms are held on option for you until {{formatDate request.request.expireAt metadata.formats.date}}
              </div>
              {{else}}
              <div class='header' style='color: {{metadata.header.color}};'>
                This offer is based upon availability and may not be applicable at a later date.
              </div>
              {{/isOnHold}}
              <div class='meta' style='color: {{metadata.header.color}};'>
                If this proposal doesn't leave you satisfied, please let {{operator.fullName}} know.
              </div>
              <div class='extra' style='color: {{metadata.header.color}};'>
                <button class='ui negative reject disabled button' id='reject-main-button'>
                  REJECT THIS OFFER
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class='four wide column computer only right aligned'>
        <img src='https://files.quotelier.net/docs/acrotel/Acrotel_Porto_Brava.jpg' alt='{{property.name}}' />
      </div>
    </div>
    <div class='expanded row'>
      <div class='sixteen wide column mobile only center aligned'>
        <div class='ui message'>
          Visit this page on your desktop computer or tablet to see more details about our offer
        </div>
      </div>
    </div>
  </div>
</div>
