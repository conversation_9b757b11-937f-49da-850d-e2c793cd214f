<div class='row offer-block' data-option-id='{{groupId}}' id='offer-{{groupId}}-block'>
  <div class='row'>
    <div class='ui compact flatten segment'>
      <div class='ui stackable divided grid'>
        <div class='row'>
          <div class='ten wide tablet twelve wide column'>
            <h2 class='ui header text-uc'>
              {{group.0.title}}
            </h2>
            <h3 class='ui header emphasize text-uc'>
               {{group.0.description}}
            </h3>
            {{#each (uniqueOptionOffersAccommodation group) as |room roomIndex|}}
              <div class='ui grid'>
                <div class='sixteen wide mobile nine wide computer column'>
                  <a class='ui large image' href='#'>
                    <img class='ui visible image' src='{{ room.photos.[0].medium }}' alt='{{room.name}}'>
                  </a>
                </div>
                <div class='sixteen wide mobile seven wide computer column'>
                  <div class='ui header h4'>
                    <a class='text-black' href='#'>
                      {{room.name}}
                    </a>
                  </div>
                  <div class='ui'>
                    {{{summarySplit room.description "<!--summary-->" "first"}}}
                  </div>
                </div>
              </div>
            {{/each}}
            {{#if group.0.rate.board.description }}
              {{#if group.0.rate.board.photo}}
                <div class='ui grid'>
                <div class='seven wide column computer only'>
                  <a class='ui image' href='#'>
                    <img class='ui rounded image' src='{{group.0.rate.board.photo}}'>
                  </a>
                </div>
                <div class='sixteen wide mobile nine wide computer column'>
                  <div class='ui header'>
                    <a class='ui small header' href='#'>
                      {{group.0.rate.board.type}}
                    </a>
                  </div>
                  <div class='description'>
                    {{group.0.rate.board.description}}
                  </div>
                </div>
              </div>
              {{/if}}
            {{/if}}
            {{#each (uniqueOptionOffersServices group) as |service serviceIndex|}}
              <div class='ui grid'>
                <div class='seven wide column computer only'>
                  <a class='ui image' href='#'>
                    <img class='ui rounded image' src='{{service.photos.[0].medium}}' alt='{{service.name}}'>
                  </a>
                </div>
                <div class='sixteen wide mobile nine wide computer column'>
                  <div class='ui header'>
                    <a class='ui small header' href='#'>
                      {{service.name}}
                    </a>
                  </div>
                  <div class='description'>
                    {{{service.description}}}
                  </div>
                </div>
              </div>
            {{/each}}
            <div class='ui basic segment'>
                {{{group.0.rate.presentation}}}
            </div>
          </div>
          <div class='sixteen wide tablet mobile four wide computer column'>
            {{#each group as |offer offerKey|}}
              <div class='ui vertical segment center aligned'>
                {{ formatPeriod offer @root.request.language metadata.formats.period }}
              </div>
              <div class='ui vertical segment center aligned'>
                {{
                  separateWithComma
                  (pluralize offer.rooms 'Room' 'Rooms')
                  (pluralize offer.adults 'Adult' 'Adults')
                  (pluralize offer.children 'Child' 'Children')
                  (pluralize offer.infants 'Infant' 'Infants')
                  (pluralize offer.nights 'Night' 'Nights')
                  ' per room '
                }}
              </div>
              {{#ifLess offer.roomRate offer.officialRate }}
                <div class='ui vertical segment center aligned'>
                  STANDARD AVERAGE RATE
                  <div class='ui strong'>
                    <del>{{ formatMoney offer.officialRate offer.currency }}</del>
                  </div>
                </div>
                <div class='ui vertical segment center aligned'>
                  YOUR AVERAGE RATE
                  <div class='ui strong emphasize'>
                    {{ calcDiscount offer }} % COMMISSION
                  </div>
                </div>
                <div class='ui vertical segment center aligned'>
                  <strong class='ui header huge'>{{ formatMoney offer.roomRate offer.currency }}</strong>
                  <span>/ night</span>
                </div>
                {{else}}
                <div class='ui vertical segment center aligned'>
                  AVERAGE RATE
                  <div>
                    <strong class='ui header huge'>{{ formatMoney offer.roomRate offer.currency }}</strong>
                    <span>/ night</span>
                  </div>
                </div>
              {{/ifLess}}
              <div class='ui vertical segment center aligned'>
                Accommodation Total: {{ formatMoney (multiply offer.roomRate offer.nights ) offer.currency }}
              </div>
              {{#compare offer.excludedCharges ">" 0 }}
              <div class='ui vertical segment center aligned'>
                Climate Resilience Tax: {{ formatMoney (multiply offer.excludedCharges offer.nights ) offer.currency }}
              </div>
              {{/compare}}
              {{#compare offer.serviceTotalRate ">" 0 }}
              <div class='ui vertical segment center aligned'>
                Service Total: {{ formatMoney offer.serviceTotalRate offer.currency }}
              </div>
              {{/compare}}
              <div class='ui vertical segment center aligned'>
                <div class='ui strong'>
                  TOTAL: {{ formatMoney (calcTotalPrice offer) offer.currency }}
                </div>
              </div>
            {{/each}}
            {{> acceptButton offer=group.[0] }}
            {{> policyLink offer=group.[0] }}
          </div>
        </div>
      </div>
    </div>
    <div class='ui hidden divider test'></div>
  </div>
</div>
