---
  metadata:
    email:
      subject: 'Acrotel | Reservation Confirmation'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      bcc: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  Dear {{@root.request.contact.nickname}},
</p>
<p>
  I'm happy to confirm your reservation regarding your upcoming stay.
</p>
<p>
  Your booking and all the services and amenities we have discussed are now confirmed!
</p>
<p>
  Please find below the accommodation summary for your records.
</p>
<h4>ACCOMMODATION SUMMARY</h4>
<h5>Reference Number: {{@root.request.id}}</h5>
{{> accommodationSummary . }}
  <p>
  Thank you once again for choosing Acrotel Hotels & Resorts for your carefree holidays. It was my pleasure communicating with you and of course, I remain at your disposal for any additional information or assistance you may require.
</p>
<p>
  Kindest regards,
</p>

{{/emailLayout}}