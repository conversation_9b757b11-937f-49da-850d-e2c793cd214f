---
    metadata:
        header:
            backgroundColor: "#ffffff !important"
            color: "#3e4453 !important"
        formats:
            date: DD-MM-YYYY
            humanDate: MMMM D YYYY
            period: [DD MMMM, DD MMMM YYYY]
        property:
            logourl: '{{{property.logourl}}}'
        i18n:
            opened:
                text: ACCEPT THIS OFFER
                classes: [positive]
            loading:
                text: LOADING
                classes: [disabled]
            accepted:
                text: ACCEPTED
                classes: [disabled, yellow]
            rejected:
                text: REJECTED
                classes: [disabled, negative]
            expired:
                text: EXPIRED
                classes: [disabled, black]
---
<!DOCTYPE html>
<html>

<head>
    <meta charset='utf-8'>
    <meta contant='IE=edge,chrome=1' http_equiv='X-UA-Compatible'>
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0' name='viewport'>
    <link href='{{assetsUrl}}proposal.css' rel='stylesheet' type='text/css'>
    <script>
        window.__quotelier__ = JSON.parse('{{{serialize lifecycleActions}}}');
    </script>
</head>

<body>

    <div class="ui warning message preview-warning hidden">
        <div class="header">
            This is a preview of the proposal that will be sent to the customer.
        </div>
        Certain functionalities may not work as expected.
    </div>

    <div class='ui flatten segments'>
        <div class="ui inverted page dimmer">
            <div class="content">
                  <div class="center">
                      <i class="massive icons"><i class="red history icon"></i><i class="corner black lock icon"></i></i>
                  </div>
            </div>
        </div>
        <div class='ui basic flatten segment' id='header' style='background-color: {{metadata.header.backgroundColor}};'>
            <div class='ui container four column stackable grid'>
                <div class='row mobile tablet only'>
                    <div class='center aligned column'>
                        <img src='{{metadata.property.logourl}}'>
                    </div>
                </div>
                <div class='row tablet only'>
                    <div class='sixteen wide column center aligned'>
                        <div class='ui message'>
                            <i class='tablet big icon'></i>
                            <i class='refresh loading icon'></i>
                            <i class='tablet big rotated icon'></i>
                        </div>
                        <div class='p'>Rotate your tablet to see more details about our offer
                        </div>
                    </div>
                </div>
            </div>
            <div class='ui container four column stackable grid'>
                <div class='row computer only'>
                    <div class='left floated column'>
                        <img src='{{metadata.property.logourl}}'>
                    </div>
                    <div class='right floated column computer only'>
                        <div class='ui horizontal list'>
                            <div class='item'>
                                <a href="{{property.url}}">
                                    <h4 class='ui icon header' style='color: {{metadata.header.color}};'>
                                        <i class='big globe icon'></i>
                                        <div class='content'>WEBSITE</div>
                                    </h4>
                                </a>
                            </div>
                            <div class='item'>
                                <a href="mailto:{{property.contact.email}}">
                                    <h4 class='ui icon header' style='color: {{metadata.header.color}};'>
                                        <i class='big envelope icon'></i>
                                        <div class='content'>CONTACT US</div>
                                    </h4>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='ui secondary basic segment'>
            <div class='ui container stackable divided grid'>
                <div class='row'>
                    <div class='ten wide column'>
                        <div class='ui tiny emphasize header'>{{formatDate request.createdAt metadata.formats.humanDate}}</div>
                         {{{request.request.message}}}
                    </div>
                    <div class='six wide expanded column computer only'>
                        <div class='ui basic segment contact'>
                            <div class='ui grid'>
                                <div class='packed row'>
                                    <div class='expanded right aligned column'>
                                        Reference No: {{request.id}}
                                    </div>
                                </div>
                                <div class='packed row'>
                                    <div class='expanded column'>
                                        <h4 class='ui image header'>
                                            <img class='ui small rounded image' src='{{operator.photo.small}}'>
                                            <div class='content text-black'>
                                                {{operator.fullName}}
                                                <div class='sub header'>
                                                    {{operator.title}}
                                                </div>
                                            </div>
                                        </h4>
                                    </div>
                                </div>
                                <div class='packed row'>
                                    {{{operator.description}}}
                                </div>
                                <div class='two column packed row text-black'>
                                    <div class='expanded column'>
                                        <i class='ui big phone icon left floated'></i>
                                        <div class='header'>Phone Number:</div>
                                        <div class='ui'>{{operator.phoneNumber}}</div>
                                    </div>
                                    <div class='expanded column'>
                                        <i class='ui big envelope icon left floated'></i>
                                        <div class='header'>Email:</div>
                                        <div class='ui'>
                                            <a href='mailto:{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'>
                                            Click to
                                            email {{operator.nickname}}
                                        </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='ui basic segment branding'>
            <div class='ui container stackable grid computer only'>
                <div class='row'>
                    <div class='ten wide column'>
                        <h2 class='ui header'>
                            {{property.name}}
                        </h2>
              	     	<h4>Welcome to {{property.name}}</h4>
                        {{{property.description}}}
                    </div>
                    <div class='six wide column'>
                        <img class='ui large rounded image' src='{{property.photos.[0].large}}'>
                    </div>
                </div>
            </div>
        </div>
        <div class='ui secondary basic segment'>
            <div class='ui very relaxed container'>
                <div class='row'>
                    <div class='column m-t-lg'>
                        <h4 class='ui center aligned header text-uc'>
                            IT IS OUR PLEASURE
                            <div class='emphasize'>
                                TO CONFIRM AVAILABILITY FOR:
                            </div>
                        </h4>
                    </div>
                </div>
                {{#each request.offers as |offer offerKey|}}
                <div class='row'>
                    <div class='ui compact flatten segment offer-block' data-offer-id='{{offerKey}}' id='offer-{{offerKey}}-block'>
                        <div class='ui stackable divided grid'>
                            <div class='row'>
                                <div class='ten wide tablet twelve wide column'>
                                    <h2 class='ui header text-uc'>
                                        {{offer.title}}
                                    </h2>
                                    <h3 class='ui header emphasize text-uc'>
                                        {{offer.description}}
                                    </h3>
                                    {{#each offer.accommodation as |room roomIndex|}}
                                    <div class='ui grid'>
                                        <div class='nine wide column computer only'>
                                            <a class='ui large image' href='#'>
                                                <img class='ui visible image' src='{{ room.photos.[0].medium }}'>
                                            </a>
                                        </div>
                                        <div class='sixteen wide mobile seven wide computer column'>
                                            <div class='ui header h4'>
                                                <a class='text-black' href='#'>
                                                    {{room.name}}
                                                </a>
                                            </div>
                                            <div class='ui'>
                                                {{{room.description}}}
                                            </div>
                                        </div>
                                    </div>
                                    {{/each}}
                                    {{#each services as |service serviceIndex|}}
                                    <div class='ui grid'>
                                        <div class='seven wide column computer only'>
                                            <a class='ui image' href='#'>
                                                <img class='ui rounded image' src='{{service.photos.[0].medium}}'>
                                            </a>
                                        </div>
                                        <div class='sixteen wide mobile nine wide computer column'>
                                            <div class='ui header'>
                                                <a class='ui small header' href='#'>
                                                    {{service.name}}
                                                </a>
                                            </div>
                                            <div class='description'>
                                                {{service.description}}
                                            </div>
                                        </div>
                                    </div>
                                    {{/each}}
                                </div>
                                <div class='sixteen wide tablet mobile four wide computer column'>
                                    <div class='ui vertical segment center aligned'>
                                        {{ formatPeriod offer ../request.language metadata.formats.period }}
                                    </div>
                                    <div class='ui vertical segment center aligned'>
                                        {{#with ../request.request}}
                                        {{
                                            separateWithComma
                                            (pluralize offer.rooms 'Room' 'Rooms')
                                            (pluralize offer.adults 'Adult' 'Adults')
                                            (pluralize offer.children 'Child' 'Children')
                                            (pluralize offer.infants 'Infant' 'Infants')
                                            (pluralize offer.nights 'Night' 'Nights')
                                        }}
                                        {{/with}}
                                    </div>
                                    {{#ifLess offer.roomRate offer.officialRate }}
                                        <div class='ui vertical segment center aligned'>
                                            STANDARD AVERAGE
                                            RATE
                                            <div class='ui strong'>
                                                <del>{{ formatMoney
                                                        offer.officialRate
                                                        offer.currency }}</del>
                                            </div>
                                        </div>
                                        <div class='ui vertical segment center aligned'>
                                            YOUR AVERAGE RATE
                                            <div class='ui strong emphasize'>
                                                {{ calcDiscount offer }} %
                                                DISCOUNT
                                            </div>
                                        </div>
                                        <div class='ui vertical segment center aligned'>
                                            <strong class='ui header huge'>{{ formatMoney
                                                    offer.roomRate
                                                    offer.currency }}</strong>
                                            <span>/ night</span>
                                        </div>
                                    {{else}}
                                        <div class='ui vertical segment center aligned'>
                                            AVERAGE RATE
                                            <div>
                                                <strong class='ui header huge'>{{ formatMoney
                                                        offer.roomRate
                                                        offer.currency }}</strong>
                                                <span>/ night</span>
                                            </div>
                                        </div>
                                    {{/ifLess}}
                                    <div class='ui vertical segment center aligned'>
                                        <div class='ui strong'>
                                            TOTAL: {{ formatMoney (calcTotalPrice offer) offer.currency }}</div>
                                        <small>All Taxes Included</small>
                                    </div>
                                    <div class='ui vertical segment center aligned'>
                                        <button class='ui fluid button text-uc offer-action-btn'
                                            data-action-prop-loading='{{{serialize @root.metadata.i18n.loading}}}'
                                            data-action-prop-opened='{{{serialize @root.metadata.i18n.opened}}}'
                                            data-action-prop-accepted='{{{serialize @root.metadata.i18n.accepted}}}'
                                            data-action-prop-rejected='{{{serialize @root.metadata.i18n.rejected}}}'
                                            data-action-prop-expired='{{{serialize @root.metadata.i18n.expired}}}'
                                            data-offer-id='{{offerKey}}'
                                            id='offer-{{offerKey}}-action-btn'>
                                        </button>
                                        <small>
                                            <a class='policy link' href='javascript:void(0);'>
                                                Payment Cancellation Policy
                                            </a>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='ui hidden divider test'></div>
                    <div class='ui policy small modal'>
                        <i class='close icon'></i>
                        <div class='header'>Payment &amp; Cancellation Policy</div>
                        <div class='content'>
                            <div class='description'>
                                {{{ offer.rate.policies.payment }}}
                                {{{ offer.rate.policies.cancellation }}}
                            </div>
                        </div>
                    </div>
                    <div class='ui accept positive small modal' data-id='{{ offerKey }}'>
                        <i class='close icon'></i>
                        <div class='header'>
                            Do you accept this offer?
                        </div>
                        <div class='content'>
                            <div class='description'>
                                We will reserve the room for you and contact you regarding all the different payment options that we offer.
                            </div>
                            <div class='ui hidden divider'></div>
                            <form class='ui form accept-offer accept-offer-form clearfix' data-offer-id="{{offerKey}}">
                                <div class='ui fluid huge input'>
                                    <input name='fullname' placeholder='Your Full Name' required min="18" max="99">
                                </div>
                                <div class='ui hidden divider'>
                                    <div class='ui checkbox'>
                                        <input name='policy' type='checkbox' required>
                                        <label>
                                            I accept the payment and cancelation policy.
                                        </label>
                                    </div>
                                    <button class='ui submit positive right floated button' type="submit">
                                        Confirm
                                    </button>
                                </div>
                                <div class='ui hidden divider'></div>
                                <div class='ui error message'>
                                    ERROR MESSAGE
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                {{/each}}
            </div>
        </div>
        <div class='ui basic segment' style='background-color: {{@root.metadata.header.backgroundColor}};'>
            <div class='ui container stackable grid'>
                <div class='row'>
                    <div class='twelve wide column disabled'>
                        <div class='ui items'>
                            <div class='item'>
                                <div class='content'>
                                    {{#isOnHold request}}
                                        <div class='header' style='color: {{metadata.header.color}};'>
                                            These rooms are held on option for
                                            you until {{formatDate
                                                request.request.expireAt
                                                metadata.formats.date}}
                                        </div>
                                    {{else}}
                                        <div class='header'>
                                            This offer is based upon availability and may not be applicable at a later date.
                                        </div>
                                    {{/isOnHold}}
                                    <div class='meta' style='color: {{metadata.header.color}};'>
                                        If this proposal doesn't leave you satisfied, please let {{operator.fullName}} know.
                                    </div>
                                    <div class='extra' style='color: {{metadata.header.color}};'>
                                        <button class='ui negative reject disabled button' id='reject-main-button'>
                                        REJECT THIS OFFER
                                    </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='four wide column computer only right aligned'>
                        <img src='{{ metadata.property.logourl}}'>
                    </div>
                </div>
                <div class='expanded row'>
                    <div class='sixteen wide column mobile only center aligned'>
                        <div class='ui message'>
                            Visit this page on your desktop computer or tablet to see more details about our offer
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class='ui negative reject small modal'>
        <i class='close icon'></i>
        <div class='header'>Thank you for letting us know!</div>
        <div class='content'>
            <div class='description'>
                We’re sorry that you do not wish to proceed with our proposal. If you could offer us some feedback, it would mean a lot!
            </div>
            <div class='ui divider'></div>
            <div class='form'>
                <form id='rejection-form'>
                    <div class='list'>
                        <div class='item'>
                            <div class='ui checkbox'>
                                <input type='checkbox' value='trip_cancelled'>
                                <label>Your offer was fine. My trip got
                                cancelled</label>
                            </div>
                        </div>
                        <div class='item'>
                            <div class='ui checkbox'>
                                <input type='checkbox' value='expensive'>
                                <label>The rate is not within the price range that
                                I had in mind</label>
                            </div>
                        </div>
                        <div class='item'>
                            <div class='ui checkbox'>
                                <input type='checkbox' value='travel_agent'>
                                <label>I’m going to book through a Travel
                                Agent</label>
                            </div>
                        </div>
                        <div class='item'>
                            <div class='ui checkbox'>
                                <input type='checkbox' value='other'>
                                <label>Other</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class='actions'>
            <div class='ui negative rejection button offer-reject-btn'>Reject</div>
        </div>
    </div>
    <div class='ui accepted small modal'>
        <i class='close icon'></i>
        <div class='header'>Thank you!</div>
        <div class='content'>
            <div class='description'>
                Your booking has been registered. Please check your inbox, the payment details have been sent to you by email.
            </div>
        </div>
    </div>
    <div class='ui rejected small modal'>
        <i class='close icon'></i>
        <div class='header'>Thank you for your feedback!</div>
        <div class='content'>
            <div class='description'>
                We are sorry to know that you did not accept this offer. We surely hope that we will welcome you as our guests in the near future either way you book.
            </div>
        </div>
    </div>

    <script src='{{assetsUrl}}proposal.js'></script>
</body>

</html>
