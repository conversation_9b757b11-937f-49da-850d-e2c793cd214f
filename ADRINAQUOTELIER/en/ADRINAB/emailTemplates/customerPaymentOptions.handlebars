---
  metadata:
    email:
      subject: '{{request.request.property.name}} | Payment Options'
      from: '{{request.operator.email}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
    formats:
        date: DD-MM-YYYY
        humanDate: MMMM D YYYY
        period: [DD MMMM, DD MMMM YYYY]
---
<p>
  Dear {{request.contact.nickname}},
</p>
<p>
  Thank you for your prompt reply.
</p>
<p>
  I am really glad to know that you wish to proceed with your booking and that we will have the pleasure to welcome you as
  our guest.
</p>
<p>
  We have now registered your booking so please take some time to read carefully our payment & cancellation policy:
</p>
  {{{offer.rate.policies.payment}}}
  {{{offer.rate.policies.cancellation}}}
{{#if request.request.expireAt}}
<p>
  Your room will be held on option until {{formatDate request.request.expireAt metadata.formats.date}}.
</p>
{{/if}}
<p>
  The payment could be arranged by the following methods.
</p>
{{#if offer.paymentUrl}}
 <p>
 <h4>Online with Credit Card</h4>
 To pay with your credit card please follow this link to <a href="{{offer.paymentUrl}}">Pay Online</a>
 </p>
{{/if}}
<p>
  <h4>Offline with Credit Card Form</h4>
  If you wish to proceed with the payment by credit card form, we highly recommend you not to send your credit card details by email.
  You could send them by fax at ### ### #### by downloading and filling out our credit card <a href="#">authorization form</a>.
</p>
<p>
  <h4>Bank Wire Transfer</h4>
  If you wish to proceed by bank wire transfer, please use our company’s <a href="#">bank details</a>
</p>
<p>
  In either case, once you proceed with the payment, please let me know which option and which method you chose, so that I
  can send you a confirmation of reservation.
</p>
<p>
  Awaiting for your response and looking forward to welcoming you as our guest!
</p>
<p>
  Best regards,
</p>
