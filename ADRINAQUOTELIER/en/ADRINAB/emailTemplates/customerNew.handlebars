---
  metadata:
    email:
      subject: '{{{request.request.property.name}}} | Reservation Proposal'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
    formats:
        date: DD-MM-YYYY
        humanDate: MMMM D YYYY
        period: [DD MMMM, DD MMMM YYYY]
---
<p>
  Dear {{request.contact.nickname}},
</p>
<p>
  Further to your kind request, I have prepared a special quote for you and uploaded it on our secure server for your perusal.
</p>
<p>
  To see this quote please click on the link below.
</p>
<p>
  <a href="{{requestLink}}">offers.adrinabeach.com</a>
</p>
{{#isOnHold request}}
<p>
  Please keep in mind that the suggested accommodation is kept on option for you until {{formatDate request.request.expireAt metadata.formats.date}}.
  </p>
{{/isOnHold}}
<p>
  I remain at your disposal and do not hesitate to contact me directly if you have any questions.
</p>
<p>
  Best regards,
</p>
