---
  metadata:
    email:
      subject: '{{request.request.property.name}} | Greetings'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
    formats:
        date: DD-MM-YYYY
        humanDate: MMMM D YYYY
        period: [DD MMMM, DD MMMM YYYY]
---
<p>
  Dear {{request.contact.nickname}},
</p>
<p>
  I trust you are doing great!
</p>
<p>
  Further to our previous correspondence, I was wondering if you had time to examine our proposal. 
</p>
<p>
  <a href="http://quote.quotelier.net/{{request.id}}"}>http://quote.quotelier.net/{{request.id}}</a>
</p>
{{#isOnHold request}}
<p>
  Please keep in mind that the suggested accommodation is kept on option for you until {{formatDate request.request.expireAt metadata.formats.date}}.
  </p>
{{/isOnHold}}
<p>
  Looking forward to your reply and I remain at your disposal, should you wish us to check other information about your unparalleled holidays at {{property.name}}.
</p>
<p>
  Kindest regards,
</p>
