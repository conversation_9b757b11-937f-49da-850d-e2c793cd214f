---
  metadata:
    email:
      subject: '{{{request.request.property.name}}} | Notice'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
---
<p>
Dear {{request.contact.nickname}},
</p>
<p>
Further to the registration of your booking a few days ago, please note that we have not yet received an email from yourself with regards to the payment.
</p>
<p>
Once you proceed with the payment, please do let me know which option and which method you chose, so that I can send you a confirmation of your reservation.
</p>
<p>
I look forward to receiving your update and I remain at your disposal.
</p>
<p>
Kindest regards,
</p>
