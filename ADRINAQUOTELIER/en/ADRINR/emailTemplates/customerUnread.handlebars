---
  metadata:
    email:
      subject: '{{{request.request.property.name}}} | Greetings'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
---
<p>
Dear {{request.contact.nickname}},
</p>
<p>
I hope that my email finds you really well!
</p>
<p>
Further to our previous communication, I am writing in order to find out if you had the chance to check our proposal and whether it was to your liking.
</p>
<p>
  <a href="http://quote.quotelier.net/{{request.id}}"}>http://quote.quotelier.net/{{request.id}}</a>
</p>
<p>
Should you wish us to check other information about your relaxing holidays at {{property.name}}, feel free to email me directly and I will be happy to be of assistance.
</p>
<p>
Kindest regards,
</p>
