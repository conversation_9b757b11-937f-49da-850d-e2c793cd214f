---
  metadata:
    email:
      subject: '{{{request.request.property.name}}} | Zahlungsmöglichkeiten'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  {{request.contact.nickname}},
</p>
<p>
   Danke für Ihre Antwort.
</p>
<p>
  Wir freuen uns über Ihre Buchung für den Aufenthalt im {{request.request.property.name}} und Sie als Gast begrüßen zu dürfen.
</p>
<p>
   Ihre Reservierung ist jetzt verbindlich. Bitte lesen Sie sich aufmerksam die nachstehenden Zahlungsbedingungen und die Stornobedingungen durch.
</p>
  {{{offer.rate.policies.payment}}}
  {{{offer.rate.policies.cancellation}}}
  {{{offer.property.disclaimer.externally_collected_taxes}}}
<p>
   Die Zahlung kann auf zwei Arten erfolgen.
</p>
{{#if offer.paymentUrl}}
 <p>
 <h4>Online mit Kreditkarte</h4>
 Zu Garantie ihrer Reservierung mit Ihrer Kredit Karte bitte folgen Sie diesem Link zu {{#eq @root.request.options.flowType "selectMany"}}
<a href="{{{stripQuerystring @root.offer.property.bookurl}}}payment/new?res_id={{@root.offer.reservationId}}&email={{@root.request.contact.email}}">Online Form</a>
{{else}}
<a href="{{{stripQuerystring @root.offer.property.bookurl}}}payment/new?res_id={{@root.offer.reservationId}}&email={{@root.request.contact.email}}&combined=1">Online Form</a>
{{/eq}}
 </p>
{{/if}}
<p>
  <h4>Banküberweisung</h4>
  Wenn Sie per Banküberweisung fortsetzen möchten, benutzen Sie bitte die Daten unseres Unternehmens. <a href="https://files.quotelier.net/docs/AMADRIAPARK/Bank_Opatjia.pdf">Bank Transfer</a>
</p>
<p>
   Auf jeden Fall, wenn Sie mit der Bezahlung fortfahren, lassen Sie mich bitte wissen, welche Option Sie gewählt haben, dass ich Ihnen die Reservierungsbestätigung senden kann.
</p>
<h4>Bedingungen und Konditionen</h4>
<p>{{{@root.request.request.property.general_terms}}}</p>
<p>
   Wir hoffen auf Ihre baldige Antwort und freuen uns auf Ihre Ankunft!
</p>
<p>
  Mit freundlichen Grüßen aus {{request.request.property.name}},
</p>

{{/emailLayout}}