---
  metadata:
    email:
      subject: '{{{request.request.property.name}}} | Zahlungsmöglichkeiten'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  {{request.contact.nickname}},
</p>
<p>
   Danke für Ihre Antwort.
</p>
<p>
  Wir freuen uns über Ihre Buchung für den Aufenthalt im {{request.request.property.name}} und Sie als Gast begrüßen zu dürfen.
</p>
<p>
   Ihre Reservierung ist jetzt verbindlich. Bitte lesen Sie sich aufmerksam die nachstehenden Zahlungsbedingungen und die Stornobedingungen durch.
</p>
  {{{offer.rate.policies.payment}}}
  {{{offer.rate.policies.cancellation}}}
  {{{offer.property.disclaimer.externally_collected_taxes}}}
<p>
   Die Zahlung kann auf zwei Arten erfolgen.
</p>
{{#if offer.paymentUrl}}
 <p>
 <h4>Online mit Kreditkarte</h4>
  Zu bezahlen mit Ihrer Kreditkarte bitte folgen Sie diesem Link zu <a href="{{offer.paymentUrl}}">Online Bezahlen</a>
 </p>
{{/if}}
<p>
  <h4>Kreditkarte (Visa, MasterCard ili AMEX)</h4>
   Wenn Sie die Zahlung mit der Kreditkarte fortsetzen möchten, empfehlen wir, dass Sie keine Kreditkarteninformationen per E-Mail senden. Sie können ein vorgesehenes Formular herunterladen, ausfüllen und die Daten per Fax senden auf ### ### ####, <a href="#">kreditne kartice</a> Kreditkarten-Autorisierungsformular.
</p>
<p>
  <h4>Banküberweisung</h4>
  Wenn Sie per Banküberweisung fortsetzen möchten, benutzen Sie bitte die Daten unseres Unternehmens. <a href="#">bankovni transfer</a>
</p>
<p>
   Auf jeden Fall, wenn Sie mit der Bezahlung fortfahren, lassen Sie mich bitte wissen, welche Option Sie gewählt haben, dass ich Ihnen die Reservierungsbestätigung senden kann.
</p>
<p>
   Wir hoffen auf Ihre baldige Antwort und freuen uns auf Ihre Ankunft!
</p>
<p>
  Mit freundlichen Grüßen aus {{request.request.property.name}},
</p>

{{/emailLayout}}