<div class='ui basic segment' style='background-color: {{@root.metadata.header.backgroundColor}};'>
  <div class='ui container stackable grid'>
    <div class='row'>
      <div class='twelve wide column disabled'>
        <div class='ui items'>
          <div class='item'>
            <div class='content'>
              {{#isOnHold request}}
              <div class='header' style='color: {{metadata.header.color}};'>
                Das Optionsdatum für die Bestätigung dieser Zimmer ist bis {{formatDate request.request.expireAt metadata.formats.date}}
              </div>
              {{else}}
              <div class='header' style='color: {{metadata.header.color}};'>
                 Dieses Angebot basiert auf der Verfügbarkeit und ist möglicherweise zu einem späteren Zeitpunkt nicht mehr verfügbar.
              </div>
              {{/isOnHold}}
              <div class='meta' style='color: {{metadata.header.color}};'>
                Wenn Sie mit diesem Vorschlag nicht zufrieden sind, lassen Sie es bitte {{operator.fullName}} wissen
              </div>
              <div class='extra' style='color: {{metadata.header.color}};'>
                <button class='ui negative reject disabled button' id='reject-main-button'>
                  DIESES ANGEBOT ABLEHNEN
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class='four wide column computer only right aligned'>
        <img src='{{ metadata.property.logourl}}' alt='{{property.name}}' />
      </div>
    </div>
    <div class='expanded row'>
      <div class='sixteen wide column mobile only center aligned'>
        <div class='ui message'>
          Für mehr Informationen über das Angebot besuchen Sie diese Seite auf Ihrem Computer oder Tablet
        </div>
      </div>
    </div>
  </div>
</div>
