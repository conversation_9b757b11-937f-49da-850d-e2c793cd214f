---
  metadata:
    email:
      subject: '{{{@root.request.request.property.name}}} | Υπενθύμιση'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      replyTo: '<EMAIL>'
      bcc: '{{request.request.property.contact.bcc}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  {{@root.request.contact.nickname}},
</p>
<p>
  Ελπίζω να είσαστε καλά.
</p>
<p>
  Σε συνέχεια της επικοινωνίας μας, ήθελα να σας ρωτήσω αν βρήκατε τον χρόνο να εξετάσετε την προσφορά που σας είχα στείλει.
</p>
<p>
  {{> proposalUrl . }}
</p>
{{> expirationNotice . }}
<p>
  Σε περίπτωση που χρειάζεστε κάποια διευκρίνiση, παρακαλώ μην διστάσετε να επικοινωνήσετε μαζί μου.
</p>
<p>
  Εν αναμονή της απάντησης σας.
</p>
<p>
  Με εκτίμηση,
</p>

{{/emailLayout}}