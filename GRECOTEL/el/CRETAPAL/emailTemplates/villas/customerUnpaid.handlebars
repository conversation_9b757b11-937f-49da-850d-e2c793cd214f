---
  metadata:
    email:
      subject: '{{{@root.request.request.property.name}}} | Ειδοποίηση'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      replyTo: '<EMAIL>'
      bcc: '{{request.request.property.contact.bcc}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  {{@root.request.contact.nickname}},
</p>
<p>
  Ελπίζω να είσαστε καλά.
</p>
<p>
  Σε συνέχεια της επικοινωνίας μας, και την αποδοχή της προσφοράς που σας είχα στείλει, θα ήθελα να σας ενημερώσω ότι δεν έχουμε λάβει ακόμα κάποια ειδοποίηση αναφορικά με την πληρωμή.
</p>
<p>
  Όταν προχωρήσετε με την πληρωμή θα σας παρακαλούσα να επικοινωνήσετε μαζί μου προκειμένου να επιβεβαιώσουμε την κράτηση σας.
</p>
<p>
  Εν αναμονή της απάντησης σας.
</p>
<p>
  Με εκτίμηση,
</p>

{{/emailLayout}}