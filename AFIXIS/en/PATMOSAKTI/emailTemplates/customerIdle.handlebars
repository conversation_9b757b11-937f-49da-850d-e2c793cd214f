---
metadata:
  email:
    subject: '{{{@root.request.request.property.name}}} | Greetings'
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>  Dear {{@root.request.contact.nickname}},</p><p>  We trust our email finds you well!</p><p>  Further to our previous correspondence, we were wondering if you had time to examine our proposal. </p><p>  {{> proposalUrl . }}</p><p>{{> onHoldInfo . }}</p><p>  Looking forward to your reply, we remain at your disposal should you wish us to check any other information about your holidays at {{@root.request.request.property.name}}.</p><p>  Kindest regards,</p>{{/emailLayout}}