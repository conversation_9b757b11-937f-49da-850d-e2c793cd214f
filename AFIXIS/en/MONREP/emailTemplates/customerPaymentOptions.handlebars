---
  metadata:
    email:
      subject: '{{{@root.request.request.property.name}}} | Payment Options'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      bcc: '{{request.request.property.contact.bcc}}'
    formats:
      date: DD-MM-YYYY
      humanDate: MMMM D YYYY
      period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  Dear {{@root.request.contact.nickname}},
</p>
<p>
  Thank you for your prompt reply!
</p>
<p>
  I am really glad to know that you wish to proceed with your booking and that we will have the pleasure to welcome you as
  our guest.
</p>
<p>
  We have now registered your booking so please take some time to read carefully our payment & cancellation policy:
</p>
  <p>{{{@root.offer.rate.policies.payment}}}</p>
  <p>{{{@root.offer.rate.policies.cancellation}}}</p>
  <p>{{{@root.offer.property.disclaimer.externally_collected_taxes}}}</p>
<p>
  The payment can be arranged by the following method.
</p>
{{> onlinePayment . }}
<p>
<p>
  Οnce you fill the form, please let me know, so that I can send you a confirmation of reservation.
</p>
<p>
  Awaiting for your response and looking forward to welcoming you as our guest!
</p>
<p>
  Best regards,
</p>

{{/emailLayout}}