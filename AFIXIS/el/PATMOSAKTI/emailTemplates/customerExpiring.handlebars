---
  metadata:
    email:
      subject: '{{{@root.request.request.property.name}}} | Ειδοποίηση'
      from: '{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'
      fromName: '{{{defaultValue request.request.property.settings.senderName operator.fullName}}}'
      bcc: '{{request.request.property.contact.bcc}}'
    formats:
        date: DD-MM-YYYY
        humanDate: MMMM D YYYY
        period: [DD MMMM, DD MMMM YYYY]
---
{{#> emailLayout}}
<p>
  {{@root.request.contact.nickname}},
</p>
<p>
  Ελπίζω να είσαστε καλά.
</p>
<p>
  Σας στέλνω μια φιλική υπενθύμιση ότι αύριο λήγει η προσφορά που σας έχω αποστείλει.
</p>
<p>
  Αν ενδιαφέρεστε να προχωρήσετε με την κράτηση σας θα πρέπει να την αποδεχθείτε εντός της ημέρας.
</p>
<p>
  {{> proposalUrl . }}
</p>
<p>
  Αν για οποιονδήποτε λόγο επιθυμείτε παράταση της προθεσμίας, ενημερώστε με εγκαίρως ώστε να εξετάσω αυτή τη δυνατότητα.
</p>
<p>
  Εν αναμονή της απάντησης σας.
</p>
<p>
  Με εκτίμηση,
</p>

{{/emailLayout}}