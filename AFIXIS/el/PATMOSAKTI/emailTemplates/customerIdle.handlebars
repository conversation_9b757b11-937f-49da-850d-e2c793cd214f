---
metadata:
  email:
    subject: '{{{@root.request.request.property.name}}} | Υπενθύμιση'
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: '{{request.request.property.contact.bcc}}'
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
{{#> emailLayout}}<p>  {{@root.request.contact.nickname}},</p><p>  Ελπίζουμε να είστε καλά.</p><p>  Σε συνέχεια της προηγούμενης επικοινωνίας μας, θα θέλαμε να ρωτήσουμε αν βρήκατε χρόνο να εξετάσετε την προσφορά που σας είχαμε στείλει.</p><p>  {{> proposalUrl . }}</p><p>  {{> onHoldInfo . }}</p><p>  Σε περίπτωση που χρειάζεστε κάποια διευκρίνιση, παρακαλώ μη διστάσετε να επικοινωνήσετε μαζί μας.</p><p>  Εν αναμονή της απάντησης σας.</p><p>  Με εκτίμηση,</p>{{/emailLayout}}