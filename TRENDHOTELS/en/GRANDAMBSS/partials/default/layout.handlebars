<!DOCTYPE html>
<html lang='{{request.language}}'>

<head>
  <meta charset='utf-8'>
  <meta contant='IE=edge,chrome=1' http_equiv='X-UA-Compatible'>
  <meta http-equiv='Content-Language' content='{{request.language}}'>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=5.0' name='viewport'>
  <title>Your offer from {{property.name}}</title>
  <link href='{{assetsUrl}}proposal.css' rel='stylesheet' type='text/css'>
  <script>
    window.__quotelier__ = JSON.parse('{{{serialize lifecycleActions}}}');
  </script>
</head>

<body>
  <div class="ui warning message preview-warning hidden">
    <div class="header">
      This is a preview of the proposal that will be sent to the customer.
    </div>
    Certain functionalities may not work as expected.
  </div>

  <div class='ui flatten segments'>
    <div class="ui inverted page dimmer">
      <div class="content">
        <div class="center">
          <i class="massive icons">
            <i class="red history icon"></i>
            <i class="corner black lock icon"></i>
          </i>
        </div>
      </div>
    </div>
    <div class='ui basic flatten segment' id='header' style='background-color: {{metadata.header.backgroundColor}};'>
      <div class='ui container four column stackable grid'>
        <div class='row mobile tablet only'>
          <div class='center aligned column'>
            <img src='{{metadata.property.logourl}}' alt='{{property.name}}' />
          </div>
        </div>
        <div class='row tablet only'>
          <div class='sixteen wide column center aligned'>
            <div class='ui message'>
              <i class='tablet big icon'></i>
              <i class='refresh loading icon'></i>
              <i class='tablet big rotated icon'></i>
            </div>
            <div class='p'>Rotate your tablet to see more details about our offer
            </div>
          </div>
        </div>
      </div>
      <div class='ui container four column stackable grid'>
        <div class='row computer only'>
          <div class='left floated column'>
            <img src='{{metadata.property.logourl}}' alt='{{property.name}}' />
          </div>
          <div class='right floated column computer only'>
            <div class='ui horizontal list'>
              <div class='item'>
                <a href="{{property.url}}" target="_blank">
                  <h4 class='ui icon header' style='color: {{metadata.header.color}};'>
                    <i class='big globe icon'></i>
                    <div class='content'>WEBSITE</div>
                  </h4>
                </a>
              </div>
              <div class='item'>
                <a href="mailto:{{property.contact.email}}">
                  <h4 class='ui icon header' style='color: {{metadata.header.color}};'>
                    <i class='big envelope icon'></i>
                    <div class='content'>CONTACT US</div>
                  </h4>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class='ui secondary basic segment'>
      <div class='ui container stackable divided grid'>
        <div class='row'>
          <div class='ten wide column'>
            <div class='ui tiny emphasize header'>{{formatDate request.createdAt metadata.formats.humanDate}}</div>
            {{{request.request.message}}}
          </div>
          <div class='six wide expanded column computer only'>
            <div class='ui basic segment contact'>
              <div class='ui grid'>
                <div class='packed row'>
                  <div class='expanded right aligned column'>
                    Reference No: {{request.id}}
                  </div>
                </div>
                <div class='packed row'>
                  <div class='expanded column'>
                    <h4 class='ui image header'>
                      <img class='ui small rounded image' src='{{operator.photo.small}}' alt='{{operator.fullName}}'>
                      <div class='content text-black'>
                        {{operator.fullName}}
                        <div class='sub header'>
                          {{operator.title}}
                        </div>
                      </div>
                    </h4>
                  </div>
                </div>
                <div class='packed row'>
                  {{{operator.description}}}
                </div>
                <div class='two column packed row text-black'>
                  <div class='expanded column'>
                    <i class='ui big phone icon left floated'></i>
                    <div class='header'>Phone Number:</div>
                    <div class='ui'><a href="tel:{{operator.phoneNumber}}">{{operator.phoneNumber}}</a></div>
                  </div>
                  <div class='expanded column'>
                    <i class='ui big envelope icon left floated'></i>
                    <div class='header'>Email:</div>
                    <div class='ui'>
                      <a href='mailto:{{{defaultValue request.request.property.settings.senderEmail operator.email}}}'>
                        Click to email {{operator.nickname}}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {{>@partial-block}}
  </div>
  <div class='ui negative reject small modal'>
    <i class='close icon'></i>
    <div class='header'>Thank you for letting us know!</div>
    <div class='content'>
      <div class='description'>
        We’re sorry that you do not wish to proceed with our proposal. If you could offer us some feedback, it would mean a lot!
      </div>
      <div class='ui divider'></div>
      <div class='form'>
        <form id='rejection-form'>
          <div class='list'>
            <div class='item'>
              <div class='ui checkbox'>
                <input type='checkbox' value='trip_cancelled'>
                <label>Your offer was fine. My trip got cancelled
                </label>
              </div>
            </div>
            <div class='item'>
              <div class='ui checkbox'>
                <input type='checkbox' value='expensive'>
                <label>The rate is not within the price range that I had in mind</label>
              </div>
            </div>
            <div class='item'>
              <div class='ui checkbox'>
                <input type='checkbox' value='travel_agent'>
                <label>I’m going to book through a Travel Agent
                </label>
              </div>
            </div>
            <div class='item'>
              <div class='ui checkbox'>
                <input type='checkbox' value='other'>
                <label>Other</label>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class='actions'>
      <div class='ui negative rejection button offer-reject-btn'>Reject</div>
    </div>
  </div>
  <div class='ui accepted small modal'>
    <i class='close icon'></i>
    <div class='header'>Thank you!</div>
    <div class='content'>
      <div class='description'>
        Your booking has been registered. Please check your inbox, the payment details have been sent to you by email.
      </div>
    </div>
  </div>
  <div class='ui rejected small modal'>
    <i class='close icon'></i>
    <div class='header'>Thank you for your feedback!</div>
    <div class='content'>
      <div class='description'>
        We are sorry to know that you did not accept this offer. We surely hope that we will welcome you as our guests in the near
        future either way you book.
      </div>
    </div>
  </div>

  <script src='{{assetsUrl}}proposalWithOptions.js'></script>
</body>
</html>
