---
metadata:
  email:
    subject: '{{{@root.request.request.property.name}}} | Reservation Confirmation'
    from: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    fromName: >-
      {{{defaultValue request.request.property.settings.senderName
      operator.fullName}}}
    bcc: >-
      {{{defaultValue request.request.property.settings.senderEmail
      operator.email}}}
    cc: ''
  formats:
    date: DD-MM-YYYY
    humanDate: MMMM D YYYY
    period:
      - DD MMMM
      - DD MMMM YYYY
---
<p>{{#> emailLayout}}</p><p>  Dear {{@root.request.contact.nickname}},</p><p>  We are happy to confirm your reservation regarding your stay on {{formatDate @root.request.request.checkin @root.metadata.formats.date}}.</p><p>  Your booking with us has been completed.</p><p>  Please find below the accommodation summary for your records.</p><h4>ACCOMMODATION SUMMARY</h4><h5>Reference Number: {{@root.request.id}}</h5><p>{{> accommodationSummary . }}  </p><p>Thank you once again for choosing {{@root.request.request.property.name}} for your upcoming holidays. </p><p>We remain at your disposal for any further inquiries and we look forward to your stay with us.</p><p> Kindest regards,</p><p>{{/emailLayout}}</p>